# نظام المحادثات المحلي (Local Chat System)

نظام محادثات يشبه WhatsApp يعمل على الشبكة المحلية بدون الحاجة للإنترنت.

## المميزات

- 💬 محادثات فورية في الوقت الفعلي
- 🌐 يعمل على الشبكة المحلية (LAN)
- 🔐 نظام تسجيل دخول آمن
- 📱 واجهة مستخدم باللغة العربية
- 👥 عرض حالة المستخدمين (متصل/غير متصل)
- 🎨 تصميم عصري وجذاب
- 📎 إرسال الملفات (صور، مستندات، صوتيات، فيديو)
- 🔊 أصوات الإشعارات والتنبيهات
- 🖼️ عرض الصور مباشرة في المحادثة
- 🎵 تشغيل الملفات الصوتية في المحادثة
- 📹 تشغيل مقاطع الفيديو
- 📁 تحميل الملفات المختلفة
- 🔇 التحكم في مستوى الصوت وإيقافه

## التقنيات المستخدمة

- **Python** - لغة البرمجة الأساسية
- **Flask** - إطار عمل الويب
- **Flask-SocketIO** - للمحادثات الفورية
- **SQLite** - قاعدة البيانات
- **Bootstrap** - تصميم الواجهة
- **jQuery** - التفاعل مع الصفحة

## متطلبات التشغيل

- Python 3.7 أو أحدث
- المتطلبات المذكورة في `requirements.txt`

## طريقة التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل الخادم

```bash
python app.py
```

### 3. الوصول للنظام

- افتح المتصفح واذهب إلى: `http://localhost:5013`
- للوصول من أجهزة أخرى في الشبكة: `http://[IP_ADDRESS]:5013`

### 4. معرفة عنوان IP الخاص بك

**على Windows:**
```cmd
ipconfig
```

**على Linux/Mac:**
```bash
ifconfig
```

## استخدام النظام

### إنشاء حساب جديد
1. اذهب إلى صفحة التسجيل
2. أدخل اسم مستخدم فريد
3. أدخل كلمة مرور قوية
4. اضغط "إنشاء الحساب"

### تسجيل الدخول
1. أدخل اسم المستخدم وكلمة المرور
2. اضغط "تسجيل الدخول"

### بدء المحادثة
1. اختر مستخدماً من القائمة الجانبية
2. اكتب رسالتك في المربع السفلي
3. اضغط Enter أو زر الإرسال

### إرسال الملفات
1. انقر على أيقونة المشبك 📎
2. اختر الملف من جهازك
3. أو اسحب الملف مباشرة إلى منطقة المحادثة

### الملفات المدعومة
- **الصور**: JPG, JPEG, PNG, GIF
- **الصوتيات**: MP3, WAV, OGG
- **الفيديو**: MP4, AVI, MOV
- **المستندات**: PDF, DOC, DOCX
- **الأرشيف**: ZIP, RAR
- **النصوص**: TXT

### التحكم في الأصوات
- استخدم أزرار التحكم في أسفل يسار الشاشة
- يمكن تشغيل/إيقاف الأصوات
- يمكن التحكم في مستوى الصوت

## هيكل المشروع

```
chat_project/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # متطلبات Python
├── db.sqlite3            # قاعدة البيانات (تُنشأ تلقائياً)
├── templates/            # قوالب HTML
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── register.html     # صفحة التسجيل
│   └── chat.html         # صفحة المحادثة الرئيسية
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css     # ملف التنسيق
│   └── js/
│       └── sounds.js     # ملف الأصوات والتنبيهات
├── uploads/              # مجلد الملفات المرفوعة
└── README.md             # هذا الملف
```

## قاعدة البيانات

### جدول المستخدمين (users)
| العمود | النوع | الوصف |
|--------|-------|-------|
| id | INTEGER | معرف المستخدم |
| username | TEXT | اسم المستخدم |
| password | TEXT | كلمة المرور المشفرة |
| is_online | BOOLEAN | حالة الاتصال |

### جدول الرسائل (messages)
| العمود | النوع | الوصف |
|--------|-------|-------|
| id | INTEGER | معرف الرسالة |
| sender | INTEGER | معرف المرسل |
| receiver | INTEGER | معرف المستقبل |
| message | TEXT | نص الرسالة |
| file_path | TEXT | مسار الملف المرفق |
| file_name | TEXT | اسم الملف الأصلي |
| file_type | TEXT | نوع الملف |
| message_type | TEXT | نوع الرسالة (text/file) |
| timestamp | DATETIME | وقت الإرسال |

## الأمان

- كلمات المرور مشفرة باستخدام SHA-256
- جلسات المستخدمين محمية
- التحقق من صحة البيانات المدخلة

## استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من أجهزة أخرى
**الحل:** تأكد من:
- تشغيل الخادم على `0.0.0.0:5013`
- إيقاف جدار الحماية أو السماح للمنفذ 5013
- التأكد من أن الأجهزة على نفس الشبكة

### المشكلة: الرسائل لا تصل فورياً
**الحل:** تأكد من:
- تحديث المتصفح
- التحقق من اتصال الشبكة
- إعادة تشغيل الخادم

## التطوير المستقبلي

- [x] إرسال الملفات والصور ✅
- [x] الإشعارات الصوتية ✅
- [ ] المجموعات الجماعية
- [ ] حفظ تاريخ المحادثات
- [ ] البحث في الرسائل
- [ ] الرموز التعبيرية (Emojis)
- [ ] الرسائل الصوتية (Voice Messages)
- [ ] مشاركة الشاشة
- [ ] الوضع المظلم (Dark Mode)
- [ ] تشفير الرسائل من طرف إلى طرف

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

**ملاحظة:** هذا النظام مصمم للاستخدام في الشبكات المحلية الآمنة. لا يُنصح باستخدامه على الإنترنت العام بدون إجراءات أمان إضافية.
