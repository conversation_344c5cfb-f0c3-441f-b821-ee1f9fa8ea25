<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المحادثات - {{ current_user }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chat-body {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        .users-sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #dee2e6;
            overflow-y: auto;
        }
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        .message-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }
        .user-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .user-item:hover {
            background-color: #f8f9fa;
        }
        .user-item.active {
            background-color: #e3f2fd;
            border-right: 3px solid #667eea;
        }
        .user-status {
            display: flex;
            align-items: center;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #6c757d;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        .message.sent {
            justify-content: flex-end;
        }
        .message.received {
            justify-content: flex-start;
        }
        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.sent .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .message.received .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
        }
        .message-time {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 5px;
        }
        .no-chat-selected {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6c757d;
            font-size: 1.2rem;
        }
        .input-group {
            border-radius: 25px;
            overflow: hidden;
        }
        .form-control {
            border: none;
            padding: 12px 20px;
        }
        .btn-send {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 20px;
            color: white;
        }
        .btn-send:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div>
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    نظام المحادثات - مرحباً {{ current_user }}
                </h4>
            </div>
            <div>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-light">
                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                </a>
            </div>
        </div>

        <!-- Chat Body -->
        <div class="chat-body">
            <!-- Users Sidebar -->
            <div class="users-sidebar">
                <div class="p-3 border-bottom">
                    <h6 class="mb-0">المستخدمون المتصلون</h6>
                </div>
                <div id="users-list">
                    {% for user in users %}
                    <div class="user-item" data-user-id="{{ user[0] }}" data-username="{{ user[1] }}">
                        <div class="user-status">
                            <span class="status-indicator {% if user[2] %}status-online{% else %}status-offline{% endif %}" 
                                  id="status-{{ user[0] }}"></span>
                            <div>
                                <div class="fw-bold">{{ user[1] }}</div>
                                <small class="text-muted">
                                    <span id="status-text-{{ user[0] }}">
                                        {% if user[2] %}متصل{% else %}غير متصل{% endif %}
                                    </span>
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Chat Area -->
            <div class="chat-area">
                <div id="no-chat" class="no-chat-selected">
                    <div class="text-center">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>اختر مستخدماً لبدء المحادثة</p>
                    </div>
                </div>
                
                <div id="chat-content" style="display: none;">
                    <div class="messages-container" id="messages-container">
                        <!-- Messages will be loaded here -->
                    </div>
                    
                    <div class="message-input-area">
                        <div class="input-group">
                            <input type="text" class="form-control" id="message-input" 
                                   placeholder="اكتب رسالتك هنا..." disabled>
                            <button class="btn btn-send" id="send-btn" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        const socket = io();
        let currentChatUser = null;
        let currentChatUserId = null;

        // Initialize socket connection
        socket.on('connect', function() {
            console.log('Connected to server');
        });

        // Handle user status updates
        socket.on('user_status', function(data) {
            const statusIndicator = document.getElementById('status-' + data.user_id);
            const statusText = document.getElementById('status-text-' + data.user_id);
            
            if (statusIndicator && statusText) {
                if (data.status === 'online') {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = 'متصل';
                } else {
                    statusIndicator.className = 'status-indicator status-offline';
                    statusText.textContent = 'غير متصل';
                }
            }
        });

        // Handle receiving messages
        socket.on('receive_message', function(data) {
            if (currentChatUserId == data.sender_id) {
                addMessage(data.message, 'received', data.timestamp, data.sender_name);
            }
        });

        // Handle message sent confirmation
        socket.on('message_sent', function(data) {
            if (currentChatUserId == data.receiver_id) {
                addMessage(data.message, 'sent', data.timestamp);
            }
        });

        // User selection
        $('.user-item').click(function() {
            $('.user-item').removeClass('active');
            $(this).addClass('active');
            
            currentChatUserId = $(this).data('user-id');
            currentChatUser = $(this).data('username');
            
            $('#no-chat').hide();
            $('#chat-content').show();
            $('#message-input').prop('disabled', false);
            $('#send-btn').prop('disabled', false);
            
            loadMessages(currentChatUserId);
        });

        // Send message
        $('#send-btn').click(sendMessage);
        $('#message-input').keypress(function(e) {
            if (e.which == 13) {
                sendMessage();
            }
        });

        function sendMessage() {
            const message = $('#message-input').val().trim();
            if (message && currentChatUserId) {
                socket.emit('send_message', {
                    receiver_id: currentChatUserId,
                    message: message
                });
                $('#message-input').val('');
            }
        }

        function loadMessages(userId) {
            $.get('/get_messages/' + userId, function(messages) {
                $('#messages-container').empty();
                messages.forEach(function(msg) {
                    const messageType = msg.sender == {{ session.user_id }} ? 'sent' : 'received';
                    addMessage(msg.message, messageType, msg.timestamp, msg.sender_name);
                });
                scrollToBottom();
            });
        }

        function addMessage(message, type, timestamp, senderName = '') {
            const messageHtml = `
                <div class="message ${type}">
                    <div class="message-bubble">
                        ${type === 'received' && senderName ? '<div class="fw-bold mb-1">' + senderName + '</div>' : ''}
                        <div>${message}</div>
                        <div class="message-time">${formatTime(timestamp)}</div>
                    </div>
                </div>
            `;
            $('#messages-container').append(messageHtml);
            scrollToBottom();
        }

        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }

        function scrollToBottom() {
            const container = document.getElementById('messages-container');
            container.scrollTop = container.scrollHeight;
        }
    </script>
</body>
</html>
