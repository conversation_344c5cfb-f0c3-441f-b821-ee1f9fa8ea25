from flask import Flask, render_template, request, redirect, url_for, session, jsonify
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import sqlite3
import hashlib
from datetime import datetime
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Database initialization
def init_db():
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            is_online BOOLEAN DEFAULT 0
        )
    ''')
    
    # Create messages table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sender INTEGER NOT NULL,
            receiver INTEGER NOT NULL,
            message TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (sender) REFERENCES users (id),
            FOREIGN KEY (receiver) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def get_user_by_username(username):
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
    user = cursor.fetchone()
    conn.close()
    return user

def get_user_by_id(user_id):
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
    user = cursor.fetchone()
    conn.close()
    return user

def get_all_users():
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, is_online FROM users')
    users = cursor.fetchall()
    conn.close()
    return users

def update_user_status(user_id, is_online):
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('UPDATE users SET is_online = ? WHERE id = ?', (is_online, user_id))
    conn.commit()
    conn.close()

def save_message(sender_id, receiver_id, message):
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute(
        'INSERT INTO messages (sender, receiver, message) VALUES (?, ?, ?)',
        (sender_id, receiver_id, message)
    )
    conn.commit()
    conn.close()

def get_messages(user1_id, user2_id):
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT m.*, u1.username as sender_name, u2.username as receiver_name
        FROM messages m
        JOIN users u1 ON m.sender = u1.id
        JOIN users u2 ON m.receiver = u2.id
        WHERE (m.sender = ? AND m.receiver = ?) OR (m.sender = ? AND m.receiver = ?)
        ORDER BY m.timestamp ASC
    ''', (user1_id, user2_id, user2_id, user1_id))
    messages = cursor.fetchall()
    conn.close()
    return messages

@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('chat'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        hashed_password = hash_password(password)
        
        user = get_user_by_username(username)
        if user and user[2] == hashed_password:
            session['user_id'] = user[0]
            session['username'] = user[1]
            update_user_status(user[0], True)
            return redirect(url_for('chat'))
        else:
            return render_template('login.html', error='اسم المستخدم أو كلمة المرور غير صحيحة')
    
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        hashed_password = hash_password(password)
        
        if get_user_by_username(username):
            return render_template('register.html', error='اسم المستخدم موجود بالفعل')
        
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        try:
            cursor.execute('INSERT INTO users (username, password) VALUES (?, ?)', 
                         (username, hashed_password))
            conn.commit()
            return redirect(url_for('login'))
        except sqlite3.IntegrityError:
            return render_template('register.html', error='حدث خطأ في التسجيل')
        finally:
            conn.close()
    
    return render_template('register.html')

@app.route('/chat')
def chat():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    users = get_all_users()
    current_user_id = session['user_id']
    users = [user for user in users if user[0] != current_user_id]
    
    return render_template('chat.html', users=users, current_user=session['username'])

@app.route('/logout')
def logout():
    if 'user_id' in session:
        update_user_status(session['user_id'], False)
        session.clear()
    return redirect(url_for('login'))

@app.route('/get_messages/<int:user_id>')
def get_user_messages(user_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'})
    
    messages = get_messages(session['user_id'], user_id)
    return jsonify([{
        'id': msg[0],
        'sender': msg[1],
        'receiver': msg[2],
        'message': msg[3],
        'timestamp': msg[4],
        'sender_name': msg[5],
        'receiver_name': msg[6]
    } for msg in messages])

# SocketIO events
@socketio.on('connect')
def on_connect():
    if 'user_id' in session:
        join_room(f"user_{session['user_id']}")
        update_user_status(session['user_id'], True)
        emit('user_status', {'user_id': session['user_id'], 'status': 'online'}, broadcast=True)

@socketio.on('disconnect')
def on_disconnect():
    if 'user_id' in session:
        leave_room(f"user_{session['user_id']}")
        update_user_status(session['user_id'], False)
        emit('user_status', {'user_id': session['user_id'], 'status': 'offline'}, broadcast=True)

@socketio.on('send_message')
def handle_message(data):
    if 'user_id' not in session:
        return
    
    sender_id = session['user_id']
    receiver_id = data['receiver_id']
    message = data['message']
    
    save_message(sender_id, receiver_id, message)
    
    # Send to receiver
    emit('receive_message', {
        'sender_id': sender_id,
        'sender_name': session['username'],
        'message': message,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }, room=f"user_{receiver_id}")
    
    # Send confirmation to sender
    emit('message_sent', {
        'receiver_id': receiver_id,
        'message': message,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

if __name__ == '__main__':
    # Create uploads directory
    if not os.path.exists('uploads'):
        os.makedirs('uploads')
    
    # Create static directories
    if not os.path.exists('static/css'):
        os.makedirs('static/css')
    if not os.path.exists('static/js'):
        os.makedirs('static/js')
    
    # Create templates directory
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    init_db()
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
